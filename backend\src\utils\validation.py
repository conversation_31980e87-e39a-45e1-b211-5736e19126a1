"""
Validation Utilities
Data validation functions for API inputs and CSV files
"""

import io
import logging
import pandas as pd
import re
from typing import Dict, Any, List, Optional
from datetime import datetime

from utils.constants import REQUIRED_CSV_COLUMNS, AIS_VALIDATION, MAX_FILE_SIZE_MB

logger = logging.getLogger(__name__)

async def validate_csv_file(file_content: bytes) -> Dict[str, Any]:
    """
    Validate CSV file structure and basic content
    
    Args:
        file_content: Raw CSV file content in bytes
        
    Returns:
        Validation result with errors, warnings, and metadata
    """
    try:
        # Check file size
        file_size_mb = len(file_content) / (1024 * 1024)
        if file_size_mb > MAX_FILE_SIZE_MB:
            return {
                'valid': False,
                'errors': [f'File size ({file_size_mb:.1f}MB) exceeds maximum allowed size ({MAX_FILE_SIZE_MB}MB)']
            }
        
        # Try to decode and parse CSV
        try:
            # Try UTF-8 first
            csv_text = file_content.decode('utf-8')
        except UnicodeDecodeError:
            try:
                # Try Latin-1 as fallback
                csv_text = file_content.decode('latin-1')
            except UnicodeDecodeError:
                return {
                    'valid': False,
                    'errors': ['File encoding not supported. Please use UTF-8 or Latin-1 encoding.']
                }
        
        # Parse CSV
        try:
            df = pd.read_csv(io.StringIO(csv_text))
        except Exception as e:
            return {
                'valid': False,
                'errors': [f'Invalid CSV format: {str(e)}']
            }
        
        # Validate CSV structure
        validation_result = await validate_csv_structure(df)
        
        # Add file metadata
        validation_result.update({
            'file_size_mb': round(file_size_mb, 2),
            'total_records': len(df),
            'columns': list(df.columns)
        })
        
        return validation_result
        
    except Exception as e:
        logger.error(f"CSV validation failed: {e}")
        return {
            'valid': False,
            'errors': [f'Validation failed: {str(e)}']
        }

async def validate_csv_structure(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Validate CSV structure and column requirements
    
    Args:
        df: Pandas DataFrame
        
    Returns:
        Validation result
    """
    errors = []
    warnings = []
    
    # Check if DataFrame is empty
    if df.empty:
        errors.append('CSV file is empty')
        return {'valid': False, 'errors': errors}
    
    # Check required columns
    missing_columns = []
    for required_col in REQUIRED_CSV_COLUMNS:
        if required_col not in df.columns:
            missing_columns.append(required_col)
    
    if missing_columns:
        errors.append(f'Missing required columns: {", ".join(missing_columns)}')
        return {'valid': False, 'errors': errors}
    
    # Check for extra columns (warn but don't fail)
    extra_columns = [col for col in df.columns if col not in REQUIRED_CSV_COLUMNS]
    if extra_columns:
        warnings.append(f'Extra columns will be ignored: {", ".join(extra_columns)}')
    
    # Validate data types and content
    content_validation = await validate_csv_content(df)
    errors.extend(content_validation['errors'])
    warnings.extend(content_validation['warnings'])
    
    # Count unique vessels
    vessel_count = 0
    if 'mmsi' in df.columns:
        try:
            vessel_count = df['mmsi'].nunique()
        except Exception:
            warnings.append('Could not count unique vessels - MMSI column may have invalid data')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'warnings': warnings,
        'vessel_count': vessel_count
    }

async def validate_csv_content(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Validate CSV content and data quality
    
    Args:
        df: Pandas DataFrame with AIS data
        
    Returns:
        Content validation result
    """
    errors = []
    warnings = []
    
    try:
        # Validate MMSI column
        if 'mmsi' in df.columns:
            mmsi_issues = validate_mmsi_column(df['mmsi'])
            if mmsi_issues['errors']:
                errors.extend(mmsi_issues['errors'])
            if mmsi_issues['warnings']:
                warnings.extend(mmsi_issues['warnings'])
        
        # Validate timestamp column
        if 'timestamp' in df.columns:
            timestamp_issues = validate_timestamp_column(df['timestamp'])
            if timestamp_issues['errors']:
                errors.extend(timestamp_issues['errors'])
            if timestamp_issues['warnings']:
                warnings.extend(timestamp_issues['warnings'])
        
        # Validate numeric columns
        numeric_columns = ['lat', 'lon', 'sog', 'cog', 'heading']
        for col in numeric_columns:
            if col in df.columns:
                numeric_issues = validate_numeric_column(df[col], col)
                if numeric_issues['errors']:
                    errors.extend(numeric_issues['errors'])
                if numeric_issues['warnings']:
                    warnings.extend(numeric_issues['warnings'])
        
        # Check for completely empty rows
        empty_rows = df.isnull().all(axis=1).sum()
        if empty_rows > 0:
            warnings.append(f'{empty_rows} completely empty rows found and will be skipped')
        
        # Check for duplicate records
        if 'mmsi' in df.columns and 'timestamp' in df.columns:
            try:
                duplicates = df.duplicated(subset=['mmsi', 'timestamp']).sum()
                if duplicates > 0:
                    warnings.append(f'{duplicates} duplicate records found (same MMSI and timestamp)')
            except Exception:
                pass  # Skip duplicate check if data types are problematic
        
    except Exception as e:
        errors.append(f'Content validation error: {str(e)}')
    
    return {'errors': errors, 'warnings': warnings}

def validate_mmsi_column(mmsi_series: pd.Series) -> Dict[str, List[str]]:
    """Validate MMSI column"""
    errors = []
    warnings = []
    
    # Check for missing values
    missing_count = mmsi_series.isnull().sum()
    if missing_count > 0:
        if missing_count == len(mmsi_series):
            errors.append('All MMSI values are missing')
        else:
            warnings.append(f'{missing_count} records have missing MMSI values')
    
    # Convert to string and validate format
    mmsi_str = mmsi_series.astype(str)
    
    # Check MMSI format (should be 7-15 digits)
    invalid_mmsi = mmsi_str[
        ~mmsi_str.str.match(r'^\d{7,15}$', na=False) & 
        (mmsi_str != 'nan')  # Exclude NaN values
    ]
    
    if len(invalid_mmsi) > 0:
        if len(invalid_mmsi) > 10:
            warnings.append(f'{len(invalid_mmsi)} records have invalid MMSI format (should be 7-15 digits)')
        else:
            warnings.append(f'Invalid MMSI values found: {invalid_mmsi.head(5).tolist()}')
    
    # Check for unrealistic MMSI values
    valid_mmsi = mmsi_str[mmsi_str.str.match(r'^\d{7,15}$', na=False)]
    if len(valid_mmsi) > 0:
        # MMSI should not start with 0
        starts_with_zero = valid_mmsi[valid_mmsi.str.startswith('0')]
        if len(starts_with_zero) > 0:
            warnings.append(f'{len(starts_with_zero)} MMSI values start with 0 (unusual but not invalid)')
    
    return {'errors': errors, 'warnings': warnings}

def validate_timestamp_column(timestamp_series: pd.Series) -> Dict[str, List[str]]:
    """Validate timestamp column"""
    errors = []
    warnings = []
    
    # Check for missing values
    missing_count = timestamp_series.isnull().sum()
    if missing_count > 0:
        if missing_count == len(timestamp_series):
            errors.append('All timestamp values are missing')
        else:
            warnings.append(f'{missing_count} records have missing timestamps')
    
    # Try to parse timestamps
    try:
        parsed_timestamps = pd.to_datetime(timestamp_series, errors='coerce')
        invalid_count = parsed_timestamps.isnull().sum() - missing_count
        
        if invalid_count > 0:
            warnings.append(f'{invalid_count} records have invalid timestamp format')
        
        # Check timestamp range (should be reasonable dates)
        valid_timestamps = parsed_timestamps.dropna()
        if len(valid_timestamps) > 0:
            min_date = valid_timestamps.min()
            max_date = valid_timestamps.max()
            
            # Check if dates are in reasonable range (not too old or in future)
            current_year = datetime.now().year
            if min_date.year < 1990:
                warnings.append(f'Some timestamps are very old (earliest: {min_date.date()})')
            if max_date.year > current_year + 1:
                warnings.append(f'Some timestamps are in the future (latest: {max_date.date()})')
    
    except Exception as e:
        warnings.append(f'Could not validate timestamps: {str(e)}')
    
    return {'errors': errors, 'warnings': warnings}

def validate_numeric_column(series: pd.Series, column_name: str) -> Dict[str, List[str]]:
    """Validate numeric columns (lat, lon, sog, cog, heading)"""
    errors = []
    warnings = []
    
    # Check for missing values
    missing_count = series.isnull().sum()
    if missing_count > 0:
        warnings.append(f'{missing_count} records have missing {column_name} values')
    
    # Try to convert to numeric
    try:
        numeric_series = pd.to_numeric(series, errors='coerce')
        non_numeric_count = numeric_series.isnull().sum() - missing_count
        
        if non_numeric_count > 0:
            warnings.append(f'{non_numeric_count} records have non-numeric {column_name} values')
        
        # Validate ranges
        valid_values = numeric_series.dropna()
        if len(valid_values) > 0 and column_name in AIS_VALIDATION:
            validation_config = AIS_VALIDATION[column_name]
            min_val = validation_config['min']
            max_val = validation_config['max']
            
            out_of_range = valid_values[
                (valid_values < min_val) | (valid_values > max_val)
            ]
            
            if len(out_of_range) > 0:
                warnings.append(
                    f'{len(out_of_range)} {column_name} values are outside valid range '
                    f'({min_val} to {max_val})'
                )
    
    except Exception as e:
        warnings.append(f'Could not validate {column_name}: {str(e)}')
    
    return {'errors': errors, 'warnings': warnings}

def validate_file_upload(file_size: int, filename: str, content_type: Optional[str] = None) -> Dict[str, Any]:
    """
    Validate file upload parameters
    
    Args:
        file_size: File size in bytes
        filename: Original filename
        content_type: MIME content type
        
    Returns:
        Validation result
    """
    errors = []
    warnings = []
    
    # Check file size
    max_size_bytes = MAX_FILE_SIZE_MB * 1024 * 1024
    if file_size > max_size_bytes:
        errors.append(f'File size ({file_size / 1024 / 1024:.1f}MB) exceeds maximum allowed ({MAX_FILE_SIZE_MB}MB)')
    
    # Check filename
    if not filename:
        errors.append('Filename is required')
    else:
        # Check file extension
        if not filename.lower().endswith('.csv'):
            errors.append('Only CSV files are allowed')
        
        # Check for problematic characters in filename
        if re.search(r'[<>:"/\\|?*]', filename):
            warnings.append('Filename contains special characters that may cause issues')
        
        # Check filename length
        if len(filename) > 255:
            errors.append('Filename is too long (maximum 255 characters)')
    
    # Check content type
    if content_type:
        allowed_types = ['text/csv', 'application/csv', 'text/plain']
        if content_type not in allowed_types:
            warnings.append(f'Unexpected content type: {content_type}')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'warnings': warnings
    }

def validate_job_parameters(total_vessels: int, filename: str) -> Dict[str, Any]:
    """
    Validate job creation parameters
    
    Args:
        total_vessels: Number of vessels to process
        filename: Job filename
        
    Returns:
        Validation result
    """
    errors = []
    warnings = []
    
    # Check vessel count
    if total_vessels <= 0:
        errors.append('No vessels found for processing')
    elif total_vessels > 10000:  # Arbitrary large limit
        warnings.append(f'Large number of vessels ({total_vessels}) may take significant time to process')
    
    # Check filename
    if not filename or len(filename.strip()) == 0:
        errors.append('Filename is required')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'warnings': warnings
    }

async def validate_api_request_rate(user_id: str, endpoint: str) -> Dict[str, Any]:
    """
    Validate API request rate for rate limiting
    
    Args:
        user_id: User identifier
        endpoint: API endpoint being accessed
        
    Returns:
        Rate limit validation result
    """
    # This is a simplified rate limiting check
    # In production, you'd use Redis or similar for distributed rate limiting
    
    # For now, just return success
    # Real implementation would check user's request history
    
    return {
        'allowed': True,
        'remaining_requests': 100,
        'reset_time_seconds': 3600
    }