# VESLINT Backend - Minimal Dependencies for Docker
# Core FastAPI and server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# HTTP client
httpx==0.25.2
requests==2.31.0

# Data processing and ML
pandas==2.1.4
numpy==1.24.4
scikit-learn==1.3.2
joblib==1.3.2

# Database
supabase==2.0.3

# Authentication (simplified)
firebase-admin==6.4.0

# File handling
python-multipart==0.0.6
aiofiles==23.2.1

# Data validation
pydantic==2.5.0

# Utilities
python-dotenv==1.0.0
structlog==23.2.0
