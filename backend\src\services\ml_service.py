"""
ML Service for Vessel Classification
Uses your trained extreme_maritime_classifier.joblib model
Optimized for fast inference and free deployment
"""

import os
import logging
import asyncio
import joblib
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import tempfile
import pickle

from services.feature_engineering import extract_vessel_features
from utils.constants import CLASS_NAMES, EXPECTED_FEATURES

logger = logging.getLogger(__name__)

class VesselClassifier:
    """
    Professional vessel classifier using your trained model
    Supports both local model loading and Hugging Face deployment
    """
    
    def __init__(self):
        self.model = None
        self.feature_columns = EXPECTED_FEATURES
        self.class_names = CLASS_NAMES
        self.model_loaded = False
        self.model_path = Path(__file__).parent.parent.parent / "models" / "maritime_classifier.joblib"
        
    async def initialize(self):
        """Initialize the ML model - loads your trained model"""
        logger.info("Initializing vessel classifier...")
        
        try:
            # Load your trained model
            await self._load_local_model()
            logger.info("✅ Vessel classifier ready for inference")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize classifier: {e}")
            # Create fallback dummy model for development
            await self._create_fallback_model()
            logger.warning("⚠️ Using fallback model - upload your trained model for production")
    
    async def _load_local_model(self):
        """Load your trained maritime_classifier.joblib model"""
        if not self.model_path.exists():
            raise FileNotFoundError(f"Model file not found: {self.model_path}")
        
        logger.info(f"Loading model from: {self.model_path}")
        
        # Load the model (your trained joblib file)
        model_data = joblib.load(self.model_path)
        
        # Handle different model storage formats
        if isinstance(model_data, dict):
            # If you saved as dict with model and metadata
            self.model = model_data.get('model')
            self.feature_columns = model_data.get('feature_columns', EXPECTED_FEATURES)
            logger.info("Loaded model with metadata from dictionary")
        else:
            # If you saved just the model
            self.model = model_data
            self.feature_columns = EXPECTED_FEATURES
            logger.info("Loaded model directly from joblib file")
        
        # Validate model
        if not hasattr(self.model, 'predict'):
            raise ValueError("Loaded object is not a valid classifier")
        
        self.model_loaded = True
        logger.info(f"Model loaded successfully - expects {len(self.feature_columns)} features")
        
        # Log model info if available
        if hasattr(self.model, 'n_features_in_'):
            logger.info(f"Model was trained on {self.model.n_features_in_} features")
    
    async def _create_fallback_model(self):
        """Create fallback model for development/testing"""
        from sklearn.dummy import DummyClassifier
        
        logger.warning("Creating fallback dummy classifier")
        self.model = DummyClassifier(strategy='stratified', random_state=42)
        
        # Fit with dummy data
        X_dummy = np.random.rand(100, len(self.feature_columns))
        y_dummy = np.random.choice([0, 1, 2, 3], size=100, p=[0.6, 0.18, 0.14, 0.08])
        self.model.fit(X_dummy, y_dummy)
        
        self.model_loaded = True
        logger.info("Fallback model ready")
    
    async def classify_vessels(self, ais_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Classify vessels from AIS data using your trained model
        
        Args:
            ais_data: DataFrame with AIS data (mmsi, timestamp, lat, lon, sog, cog, heading)
            
        Returns:
            List of classification results for each vessel
        """
        if not self.model_loaded:
            raise RuntimeError("Model not initialized. Call initialize() first.")
        
        try:
            logger.info(f"Starting classification for {len(ais_data)} AIS records")
            
            # Extract features using your feature engineering pipeline
            features_df = await extract_vessel_features(ais_data)
            
            if features_df.empty:
                logger.warning("No features extracted from AIS data")
                return []
            
            logger.info(f"Extracted features for {len(features_df)} vessels")
            
            # Prepare features for your model
            X, vessel_ids = await self._prepare_model_input(features_df)
            
            # Make predictions
            predictions = await self._predict(X)
            
            # Format results
            results = await self._format_results(vessel_ids, predictions, X)
            
            logger.info(f"Successfully classified {len(results)} vessels")
            return results
            
        except Exception as e:
            logger.error(f"Classification failed: {e}")
            raise
    
    async def _prepare_model_input(self, features_df: pd.DataFrame) -> Tuple[np.ndarray, List[str]]:
        """Prepare features for your trained model"""
        
        # Get vessel IDs
        vessel_ids = features_df['mmsi'].astype(str).tolist() if 'mmsi' in features_df.columns else []
        
        # Ensure all expected features are present
        missing_features = [col for col in self.feature_columns if col not in features_df.columns and col != 'mmsi']
        if missing_features:
            logger.warning(f"Adding missing features with default values: {missing_features}")
            for feature in missing_features:
                features_df[feature] = 0.0
        
        # Select only the features your model expects (excluding mmsi)
        model_features = [col for col in self.feature_columns if col != 'mmsi' and col in features_df.columns]
        X = features_df[model_features].copy()
        
        # Handle NaN values
        X = X.fillna(0)
        
        # Ensure all values are numeric
        for col in X.columns:
            X[col] = pd.to_numeric(X[col], errors='coerce').fillna(0)
        
        logger.info(f"Prepared {X.shape[0]} samples with {X.shape[1]} features")
        return X.values, vessel_ids
    
    async def _predict(self, X: np.ndarray) -> Dict[str, np.ndarray]:
        """Make predictions using your trained model"""
        try:
            # Get class predictions
            predictions = self.model.predict(X)
            
            # Get probabilities if available
            probabilities = None
            if hasattr(self.model, 'predict_proba'):
                try:
                    probabilities = self.model.predict_proba(X)
                except Exception as e:
                    logger.warning(f"Could not get prediction probabilities: {e}")
            
            return {
                'predictions': predictions,
                'probabilities': probabilities
            }
            
        except Exception as e:
            logger.error(f"Model prediction error: {e}")
            # Fallback predictions
            predictions = np.zeros(len(X), dtype=int)
            return {'predictions': predictions, 'probabilities': None}
    
    async def _format_results(self, vessel_ids: List[str], predictions: Dict[str, np.ndarray], X: np.ndarray) -> List[Dict[str, Any]]:
        """Format prediction results"""
        results = []
        
        pred_array = predictions['predictions']
        prob_array = predictions['probabilities']
        
        for i, vessel_id in enumerate(vessel_ids):
            pred_class = int(pred_array[i])
            
            result = {
                'mmsi': vessel_id,
                'prediction': pred_class,
                'class_name': self.class_names.get(pred_class, 'UNKNOWN'),
                'confidence': 0.0,
                'processing_timestamp': pd.Timestamp.now().isoformat()
            }
            
            # Add probabilities if available
            if prob_array is not None and i < len(prob_array):
                probs = prob_array[i]
                result['confidence'] = float(np.max(probs))
                result['class_probabilities'] = {
                    self.class_names[j]: float(probs[j]) 
                    for j in range(len(probs)) 
                    if j in self.class_names
                }
            
            results.append(result)
        
        return results
    
    async def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        if not self.model_loaded:
            return {"status": "not_loaded"}
        
        info = {
            "status": "loaded",
            "model_type": type(self.model).__name__,
            "expected_features": len(self.feature_columns),
            "feature_names": self.feature_columns,
            "class_names": self.class_names,
            "model_path": str(self.model_path)
        }
        
        # Add model-specific info if available
        if hasattr(self.model, 'n_features_in_'):
            info["trained_features"] = self.model.n_features_in_
        
        if hasattr(self.model, 'classes_'):
            info["model_classes"] = self.model.classes_.tolist()
        
        return info
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for the ML service"""
        try:
            if not self.model_loaded:
                return {"status": "unhealthy", "reason": "Model not loaded"}
            
            # Quick prediction test
            test_input = np.random.rand(1, len(self.feature_columns))
            _ = self.model.predict(test_input)
            
            return {
                "status": "healthy",
                "model_loaded": True,
                "features_count": len(self.feature_columns)
            }
        except Exception as e:
            return {
                "status": "unhealthy", 
                "reason": str(e)
            }
    
    async def cleanup(self):
        """Cleanup resources"""
        logger.info("Cleaning up ML service resources")
        self.model = None
        self.model_loaded = False

# Global classifier instance
_classifier = None

async def get_classifier() -> VesselClassifier:
    """Get the global classifier instance"""
    global _classifier
    if _classifier is None:
        _classifier = VesselClassifier()
        await _classifier.initialize()
    return _classifier