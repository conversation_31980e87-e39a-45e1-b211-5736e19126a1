"""
Health Check API Routes
System health monitoring and diagnostics
"""

import logging
import os
import time
import psutil
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from datetime import datetime, timezone

from database.supabase_client import test_connection
from services.ml_service import get_classifier
from utils.constants import API_VERSION, APP_NAME, MODEL_METRICS

logger = logging.getLogger(__name__)
router = APIRouter()

# Store startup time for uptime calculation
startup_time = time.time()

@router.get("/health")
async def health_check():
    """
    Basic health check endpoint for Render
    Returns 200 if service is running
    """
    return {
        "status": "healthy",
        "service": APP_NAME,
        "version": API_VERSION,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "environment": os.getenv("ENVIRONMENT", "unknown")
    }

@router.get("/health/detailed")
async def detailed_health_check():
    """
    Detailed health check with component status
    """
    health_status = {
        "service": APP_NAME,
        "version": API_VERSION,
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "uptime_seconds": round(time.time() - startup_time, 2),
        "environment": os.getenv("ENVIRONMENT", "production"),
        "components": {}
    }
    
    overall_healthy = True
    
    # Check database connectivity
    try:
        db_healthy = await test_connection()
        health_status["components"]["database"] = {
            "status": "healthy" if db_healthy else "unhealthy",
            "type": "supabase",
            "response_time_ms": None  # Could add timing here
        }
        if not db_healthy:
            overall_healthy = False
    except Exception as e:
        health_status["components"]["database"] = {
            "status": "unhealthy",
            "type": "supabase",
            "error": str(e)
        }
        overall_healthy = False
    
    # Check ML model
    try:
        classifier = await get_classifier()
        ml_health = await classifier.health_check()
        health_status["components"]["ml_model"] = {
            "status": ml_health["status"],
            "model_loaded": ml_health.get("model_loaded", False),
            "features_count": ml_health.get("features_count", 0)
        }
        if ml_health["status"] != "healthy":
            overall_healthy = False
    except Exception as e:
        health_status["components"]["ml_model"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        overall_healthy = False
    
    # System resources (for Render monitoring)
    try:
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        health_status["components"]["system"] = {
            "status": "healthy",
            "memory": {
                "total_mb": round(memory.total / 1024 / 1024, 1),
                "available_mb": round(memory.available / 1024 / 1024, 1),
                "percent_used": memory.percent
            },
            "disk": {
                "total_gb": round(disk.total / 1024 / 1024 / 1024, 1),
                "free_gb": round(disk.free / 1024 / 1024 / 1024, 1),
                "percent_used": round((disk.used / disk.total) * 100, 1)
            },
            "cpu_percent": psutil.cpu_percent(interval=1)
        }
        
        # Check for resource warnings
        if memory.percent > 90:
            health_status["components"]["system"]["warnings"] = ["High memory usage"]
            
        if disk.free < 1024 * 1024 * 1024:  # Less than 1GB free
            health_status["components"]["system"]["warnings"] = health_status["components"]["system"].get("warnings", []) + ["Low disk space"]
            
    except Exception as e:
        health_status["components"]["system"] = {
            "status": "unknown",
            "error": str(e)
        }
    
    # Update overall status
    health_status["status"] = "healthy" if overall_healthy else "unhealthy"
    
    return health_status

@router.get("/health/ready")
async def readiness_check():
    """
    Kubernetes-style readiness probe
    Returns 200 when service is ready to handle requests
    """
    try:
        # Check critical dependencies
        db_healthy = await test_connection()
        
        classifier = await get_classifier()
        ml_health = await classifier.health_check()
        
        if db_healthy and ml_health["status"] == "healthy":
            return {
                "status": "ready",
                "message": "Service is ready to handle requests"
            }
        else:
            raise HTTPException(
                status_code=503,
                detail={
                    "status": "not_ready",
                    "message": "Service dependencies not ready",
                    "database": "healthy" if db_healthy else "unhealthy",
                    "ml_model": ml_health["status"]
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail={
                "status": "not_ready",
                "message": "Readiness check failed",
                "error": str(e)
            }
        )

@router.get("/health/live")
async def liveness_check():
    """
    Kubernetes-style liveness probe
    Returns 200 if service is alive (basic process check)
    """
    return {
        "status": "alive",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "uptime_seconds": round(time.time() - startup_time, 2)
    }

@router.get("/version")
async def get_version_info():
    """
    Get detailed version and build information
    """
    return {
        "service": APP_NAME,
        "api_version": API_VERSION,
        "build_timestamp": os.getenv("BUILD_TIMESTAMP", "unknown"),
        "git_commit": os.getenv("GIT_COMMIT", "unknown"),
        "environment": os.getenv("ENVIRONMENT", "production"),
        "python_version": os.getenv("PYTHON_VERSION", "unknown"),
        "dependencies": {
            "fastapi": "0.104.1",
            "pandas": "2.1.3",
            "scikit-learn": "1.3.2",
            "supabase": "2.0.3"
        }
    }

@router.get("/metrics")
async def get_metrics():
    """
    Prometheus-style metrics endpoint
    """
    try:
        # Basic service metrics
        uptime = time.time() - startup_time
        memory = psutil.virtual_memory()
        
        # Database connection test timing
        db_start = time.time()
        db_healthy = await test_connection()
        db_response_time = (time.time() - db_start) * 1000  # milliseconds
        
        # ML model metrics
        classifier = await get_classifier()
        model_info = await classifier.get_model_info()
        
        metrics = {
            "service_uptime_seconds": round(uptime, 2),
            "service_memory_usage_bytes": memory.used,
            "service_memory_usage_percent": memory.percent,
            "database_healthy": 1 if db_healthy else 0,
            "database_response_time_ms": round(db_response_time, 2),
            "ml_model_loaded": 1 if model_info.get("status") == "loaded" else 0,
            "ml_model_features_count": model_info.get("expected_features", 0),
            "ml_model_accuracy": MODEL_METRICS.get("accuracy", 0),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to collect metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to collect metrics")

@router.get("/status")
async def get_service_status():
    """
    Service status page information
    """
    try:
        uptime = time.time() - startup_time
        
        # Format uptime
        hours = int(uptime // 3600)
        minutes = int((uptime % 3600) // 60)
        seconds = int(uptime % 60)
        uptime_formatted = f"{hours}h {minutes}m {seconds}s"
        
        # Get system info
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Check component health
        db_healthy = await test_connection()
        classifier = await get_classifier()
        ml_health = await classifier.health_check()
        model_info = await classifier.get_model_info()
        
        status = {
            "service": {
                "name": APP_NAME,
                "version": API_VERSION,
                "environment": os.getenv("ENVIRONMENT", "production"),
                "uptime": uptime_formatted,
                "status": "operational"
            },
            "components": {
                "api": {
                    "status": "operational",
                    "description": "REST API endpoints"
                },
                "database": {
                    "status": "operational" if db_healthy else "degraded",
                    "description": "Supabase PostgreSQL database",
                    "provider": "Supabase"
                },
                "ml_model": {
                    "status": "operational" if ml_health["status"] == "healthy" else "degraded",
                    "description": "Vessel classification model",
                    "model_type": model_info.get("model_type", "unknown"),
                    "accuracy": f"{MODEL_METRICS.get('accuracy', 0) * 100:.1f}%"
                },
                "file_storage": {
                    "status": "operational",
                    "description": "Supabase Storage for CSV files",
                    "provider": "Supabase"
                }
            },
            "system": {
                "memory_usage": f"{memory.percent:.1f}%",
                "disk_usage": f"{(disk.used / disk.total) * 100:.1f}%",
                "cpu_usage": f"{psutil.cpu_percent(interval=1):.1f}%"
            },
            "features": {
                "vessel_classification": "operational",
                "real_time_monitoring": "operational",
                "batch_processing": "operational",
                "api_authentication": "operational"
            }
        }
        
        return status
        
    except Exception as e:
        logger.error(f"Failed to get service status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get service status")