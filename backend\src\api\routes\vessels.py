"""
Vessel Classification API Routes
Direct vessel classification endpoints
"""

import logging
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
import pandas as pd
import io

from models.vessel import VesselClassificationRequest, VesselClassificationResponse, VesselData
from models.response import SuccessResponse
from services.ml_service import get_classifier
from services.csv_processor import validate_ais_data
from utils.auth import get_current_user
from utils.validation import validate_csv_file
from utils.constants import MAX_FILE_SIZE_MB, CLASS_NAMES, CLASS_DESCRIPTIONS

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/classify", response_model=List[VesselClassificationResponse])
async def classify_vessels_direct(
    file: UploadFile = File(...),
    user_id: str = Depends(get_current_user)
):
    """
    Direct vessel classification endpoint
    For small datasets that can be processed immediately (< 100 vessels)
    """
    try:
        # Validate file
        if not file.filename or not file.filename.endswith('.csv'):
            raise HTTPException(status_code=400, detail="Only CSV files are allowed")
        
        if file.size and file.size > MAX_FILE_SIZE_MB * 1024 * 1024:
            raise HTTPException(
                status_code=400, 
                detail=f"File size exceeds {MAX_FILE_SIZE_MB}MB limit"
            )
        
        logger.info(f"Direct classification request from user {user_id}, file: {file.filename}")
        
        # Read and validate CSV
        file_content = await file.read()
        
        # Validate CSV structure
        validation_result = await validate_csv_file(file_content)
        if not validation_result['valid']:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid CSV format: {', '.join(validation_result['errors'])}"
            )
        
        # Check vessel count limit for direct processing
        if validation_result['vessel_count'] > 100:
            raise HTTPException(
                status_code=400,
                detail=f"Too many vessels ({validation_result['vessel_count']}) for direct processing. "
                       f"Use the job-based endpoint for datasets with > 100 vessels."
            )
        
        # Parse CSV data
        df = pd.read_csv(io.StringIO(file_content.decode('utf-8')))
        
        # Validate AIS data
        ais_validation = await validate_ais_data(df)
        if not ais_validation['valid']:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid AIS data: {', '.join(ais_validation['errors'])}"
            )
        
        # Get ML classifier
        classifier = await get_classifier()
        
        # Classify vessels
        logger.info(f"Classifying {len(df)} AIS records for {validation_result['vessel_count']} vessels")
        classification_results = await classifier.classify_vessels(df)
        
        # Format response
        responses = []
        for result in classification_results:
            responses.append(VesselClassificationResponse(
                mmsi=result['mmsi'],
                predicted_class=result['prediction'],
                class_name=result['class_name'],
                confidence=result['confidence'],
                class_probabilities=result.get('class_probabilities', {}),
                processing_timestamp=result['processing_timestamp']
            ))
        
        logger.info(f"Successfully classified {len(responses)} vessels")
        return responses
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Direct classification failed: {e}")
        raise HTTPException(status_code=500, detail="Classification failed")

@router.post("/classify-json", response_model=List[VesselClassificationResponse])
async def classify_vessels_json(
    request: VesselClassificationRequest,
    user_id: str = Depends(get_current_user)
):
    """
    Classify vessels from JSON payload
    For API integrations and small datasets
    """
    try:
        logger.info(f"JSON classification request from user {user_id} for {len(request.vessels)} vessels")
        
        # Validate vessel count
        if len(request.vessels) > 100:
            raise HTTPException(
                status_code=400,
                detail=f"Too many vessels ({len(request.vessels)}) for direct processing. Maximum: 100"
            )
        
        if len(request.vessels) == 0:
            raise HTTPException(status_code=400, detail="No vessel data provided")
        
        # Convert to DataFrame
        vessel_records = []
        for vessel in request.vessels:
            for ais_point in vessel.ais_data:
                vessel_records.append({
                    'mmsi': vessel.mmsi,
                    'timestamp': ais_point.timestamp,
                    'lat': ais_point.lat,
                    'lon': ais_point.lon,
                    'sog': ais_point.sog,
                    'cog': ais_point.cog,
                    'heading': ais_point.heading
                })
        
        df = pd.DataFrame(vessel_records)
        
        # Validate AIS data
        ais_validation = await validate_ais_data(df)
        if not ais_validation['valid']:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid AIS data: {', '.join(ais_validation['errors'])}"
            )
        
        # Get ML classifier
        classifier = await get_classifier()
        
        # Classify vessels
        classification_results = await classifier.classify_vessels(df)
        
        # Format response
        responses = []
        for result in classification_results:
            responses.append(VesselClassificationResponse(
                mmsi=result['mmsi'],
                predicted_class=result['prediction'],
                class_name=result['class_name'],
                confidence=result['confidence'],
                class_probabilities=result.get('class_probabilities', {}),
                processing_timestamp=result['processing_timestamp']
            ))
        
        logger.info(f"Successfully classified {len(responses)} vessels from JSON")
        return responses
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"JSON classification failed: {e}")
        raise HTTPException(status_code=500, detail="Classification failed")

@router.get("/classes")
async def get_vessel_classes():
    """Get information about vessel classes"""
    return {
        "classes": [
            {
                "id": class_id,
                "name": class_name,
                "description": CLASS_DESCRIPTIONS.get(class_name, "")
            }
            for class_id, class_name in CLASS_NAMES.items()
        ],
        "total_classes": len(CLASS_NAMES)
    }

@router.get("/model-info")
async def get_model_info():
    """Get information about the ML model"""
    try:
        classifier = await get_classifier()
        model_info = await classifier.get_model_info()
        
        return {
            "model": model_info,
            "classes": CLASS_NAMES,
            "class_descriptions": CLASS_DESCRIPTIONS,
            "supported_features": len(model_info.get('feature_names', [])),
            "api_version": "v1"
        }
        
    except Exception as e:
        logger.error(f"Failed to get model info: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve model information")

@router.post("/validate-csv")
async def validate_csv_endpoint(
    file: UploadFile = File(...),
    user_id: str = Depends(get_current_user)
):
    """
    Validate CSV file format without processing
    Useful for frontend validation before uploading
    """
    try:
        # Basic file validation
        if not file.filename or not file.filename.endswith('.csv'):
            raise HTTPException(status_code=400, detail="Only CSV files are allowed")
        
        if file.size and file.size > MAX_FILE_SIZE_MB * 1024 * 1024:
            raise HTTPException(
                status_code=400, 
                detail=f"File size exceeds {MAX_FILE_SIZE_MB}MB limit"
            )
        
        # Read and validate content
        file_content = await file.read()
        validation_result = await validate_csv_file(file_content)
        
        if validation_result['valid']:
            # Additional AIS data validation
            df = pd.read_csv(io.StringIO(file_content.decode('utf-8')))
            ais_validation = await validate_ais_data(df)
            
            return {
                "valid": ais_validation['valid'],
                "vessel_count": validation_result['vessel_count'],
                "total_records": validation_result['total_records'],
                "columns": validation_result['columns'],
                "errors": validation_result['errors'] + ais_validation['errors'],
                "warnings": ais_validation.get('warnings', []),
                "processing_recommendation": (
                    "direct" if validation_result['vessel_count'] <= 100 
                    else "job-based"
                ),
                "estimated_processing_time": _estimate_processing_time(
                    validation_result['vessel_count']
                )
            }
        else:
            return {
                "valid": False,
                "errors": validation_result['errors']
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"CSV validation failed: {e}")
        raise HTTPException(status_code=500, detail="CSV validation failed")

@router.get("/sample-csv")
async def get_sample_csv():
    """
    Get sample CSV format for reference
    """
    sample_data = [
        {
            "mmsi": "123456789",
            "timestamp": "2024-01-01T00:00:00Z",
            "lat": 40.7128,
            "lon": -74.0060,
            "sog": 12.5,
            "cog": 180,
            "heading": 175
        },
        {
            "mmsi": "123456789",
            "timestamp": "2024-01-01T00:05:00Z",
            "lat": 40.7130,
            "lon": -74.0058,
            "sog": 12.8,
            "cog": 182,
            "heading": 178
        },
        {
            "mmsi": "987654321",
            "timestamp": "2024-01-01T00:00:00Z",
            "lat": 51.5074,
            "lon": -0.1278,
            "sog": 8.2,
            "cog": 90,
            "heading": 95
        }
    ]
    
    return {
        "format_description": "AIS data in CSV format with required columns",
        "required_columns": [
            "mmsi - Maritime Mobile Service Identity (vessel identifier)",
            "timestamp - ISO 8601 timestamp (YYYY-MM-DDTHH:MM:SSZ)",
            "lat - Latitude (-90 to 90 degrees)",
            "lon - Longitude (-180 to 180 degrees)",  
            "sog - Speed over ground (knots)",
            "cog - Course over ground (0-360 degrees)",
            "heading - Vessel heading (0-360 degrees)"
        ],
        "sample_data": sample_data,
        "notes": [
            "Each vessel (MMSI) should have multiple AIS points for accurate classification",
            "Minimum 2 AIS points per vessel required",
            "Timestamps should be in chronological order per vessel",
            "Maximum file size: 50MB",
            "Maximum vessels for direct processing: 100"
        ]
    }

def _estimate_processing_time(vessel_count: int) -> Dict[str, Any]:
    """Estimate processing time based on vessel count"""
    # Rough estimates based on your model performance
    base_time_per_vessel = 0.1  # seconds
    feature_extraction_overhead = vessel_count * 0.05
    classification_time = vessel_count * base_time_per_vessel
    
    total_seconds = feature_extraction_overhead + classification_time
    
    return {
        "estimated_seconds": round(total_seconds, 1),
        "estimated_minutes": round(total_seconds / 60, 1),
        "note": "Actual time may vary based on data complexity and system load"
    }