"""
Supabase Client for VESLINT Backend.

This module provides a comprehensive Supabase client with connection pooling,
error handling, real-time subscriptions, and storage management.
"""

import os
import json
import logging
import asyncio
import aiohttp
from typing import Dict, Any, Optional, List, Union, Callable
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from urllib.parse import urljoin

# Module-level logger
logger = logging.getLogger(__name__)

# =============================================================================
# Supabase Configuration
# =============================================================================

@dataclass
class SupabaseConfig:
    """Supabase client configuration."""
    
    url: str
    anon_key: str
    service_role_key: str
    timeout: int = 30
    max_connections: int = 100
    enable_logging: bool = True

# =============================================================================
# Custom Exceptions
# =============================================================================

class SupabaseError(Exception):
    """Base exception for Supabase operations."""
    pass

class SupabaseConnectionError(SupabaseError):
    """Supabase connection error."""
    pass

class SupabaseAuthError(SupabaseError):
    """Supabase authentication error."""
    pass

class SupabaseQueryError(SupabaseError):
    """Supabase query execution error."""
    pass

# =============================================================================
# Supabase Manager
# =============================================================================

class SupabaseManager:
    """Comprehensive Supabase client manager."""
    
    def __init__(self, url: str, key: str, service_role_key: str):
        """Initialize Supabase manager."""
        self.config = SupabaseConfig(
            url=url,
            anon_key=key,
            service_role_key=service_role_key
        )
        
        self.session: Optional[aiohttp.ClientSession] = None
        self.realtime_subscriptions: Dict[str, Any] = {}
        self._initialized = False
        
        # API endpoints
        self.rest_url = urljoin(self.config.url, "/rest/v1/")
        self.auth_url = urljoin(self.config.url, "/auth/v1/")
        self.storage_url = urljoin(self.config.url, "/storage/v1/")
        self.realtime_url = self.config.url.replace("https://", "wss://") + "/realtime/v1/websocket"
    
    async def initialize(self) -> None:
        """Initialize the Supabase client."""
        if self._initialized:
            return
            
        try:
            # Create HTTP session with connection pooling
            connector = aiohttp.TCPConnector(
                limit=self.config.max_connections,
                limit_per_host=20,
                ttl_dns_cache=300,
                use_dns_cache=True
            )
            
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers=self._get_default_headers()
            )
            
            # Test connection
            await self._test_connection()
            
            self._initialized = True
            logger.info("Supabase client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            raise SupabaseConnectionError(f"Initialization failed: {e}")
    
    async def cleanup(self) -> None:
        """Cleanup resources."""
        try:
            # Close realtime subscriptions
            for subscription_id in list(self.realtime_subscriptions.keys()):
                await self._unsubscribe_realtime(subscription_id)
            
            # Close HTTP session
            if self.session:
                await self.session.close()
                
            self._initialized = False
            logger.info("Supabase client cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during Supabase cleanup: {e}")
    
    def _get_default_headers(self, use_service_role: bool = False) -> Dict[str, str]:
        """Get default headers for API requests."""
        key = self.config.service_role_key if use_service_role else self.config.anon_key
        
        return {
            "apikey": key,
            "Authorization": f"Bearer {key}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": "VESLINT-Backend/1.0"
        }
    
    async def _test_connection(self) -> None:
        """Test database connection."""
        try:
            response = await self._make_request(
                "GET",
                "health",
                use_service_role=True
            )
            
            if response.get("status") != "ok":
                raise SupabaseConnectionError("Health check failed")
                
            logger.info("Supabase connection test successful")
            
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            raise
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
        use_service_role: bool = False,
        base_url: Optional[str] = None
    ) -> Any:
        """Make HTTP request to Supabase API."""
        if not self.session:
            raise SupabaseConnectionError("Client not initialized")
        
        url = urljoin(base_url or self.rest_url, endpoint)
        headers = self._get_default_headers(use_service_role)
        
        try:
            async with self.session.request(
                method=method,
                url=url,
                json=data,
                params=params,
                headers=headers
            ) as response:
                
                # Handle different response types
                content_type = response.headers.get('content-type', '')
                
                if 'application/json' in content_type:
                    result = await response.json()
                else:
                    result = await response.text()
                
                # Check for errors
                if response.status >= 400:
                    error_msg = result if isinstance(result, str) else result.get('message', 'Unknown error')
                    raise SupabaseQueryError(f"HTTP {response.status}: {error_msg}")
                
                return result
                
        except aiohttp.ClientError as e:
            logger.error(f"Request failed: {e}")
            raise SupabaseConnectionError(f"Request failed: {e}")
    
    # =============================================================================
    # Database Operations
    # =============================================================================
    
    async def select(
        self,
        table: str,
        columns: str = "*",
        filters: Optional[Dict] = None,
        order_by: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[Dict]:
        """Select data from table."""
        params = {"select": columns}
        
        # Add filters
        if filters:
            for key, value in filters.items():
                if key in ['limit', 'offset']:
                    continue
                params[f"{key}"] = f"eq.{value}"
        
        # Add ordering
        if order_by:
            params["order"] = order_by
        
        # Add pagination
        if limit:
            params["limit"] = str(limit)
        if offset:
            params["offset"] = str(offset)
        
        try:
            result = await self._make_request("GET", table, params=params, use_service_role=True)
            return result if isinstance(result, list) else [result] if result else []
            
        except Exception as e:
            logger.error(f"Select operation failed: {e}")
            raise SupabaseQueryError(f"Select failed: {e}")
    
    async def insert(self, table: str, data: Union[Dict, List[Dict]], returning: str = "*") -> Any:
        """Insert data into table."""
        params = {"returning": returning}
        
        try:
            result = await self._make_request(
                "POST", 
                table, 
                data=data, 
                params=params, 
                use_service_role=True
            )
            return result
            
        except Exception as e:
            logger.error(f"Insert operation failed: {e}")
            raise SupabaseQueryError(f"Insert failed: {e}")
    
    async def update(
        self,
        table: str,
        data: Dict,
        filters: Dict,
        returning: str = "*"
    ) -> Any:
        """Update data in table."""
        params = {"returning": returning}
        
        # Add filters to params
        for key, value in filters.items():
            params[f"{key}"] = f"eq.{value}"
        
        try:
            result = await self._make_request(
                "PATCH", 
                table, 
                data=data, 
                params=params, 
                use_service_role=True
            )
            return result
            
        except Exception as e:
            logger.error(f"Update operation failed: {e}")
            raise SupabaseQueryError(f"Update failed: {e}")
    
    async def delete(self, table: str, filters: Dict, returning: str = "*") -> Any:
        """Delete data from table."""
        params = {"returning": returning}
        
        # Add filters to params
        for key, value in filters.items():
            params[f"{key}"] = f"eq.{value}"
        
        try:
            result = await self._make_request(
                "DELETE", 
                table, 
                params=params, 
                use_service_role=True
            )
            return result
            
        except Exception as e:
            logger.error(f"Delete operation failed: {e}")
            raise SupabaseQueryError(f"Delete failed: {e}")
    
    async def upsert(self, table: str, data: Union[Dict, List[Dict]], returning: str = "*") -> Any:
        """Upsert data (insert or update)."""
        params = {
            "returning": returning,
            "on_conflict": "id"  # Assuming 'id' is the primary key
        }
        
        try:
            result = await self._make_request(
                "POST", 
                table, 
                data=data, 
                params=params, 
                use_service_role=True
            )
            return result
            
        except Exception as e:
            logger.error(f"Upsert operation failed: {e}")
            raise SupabaseQueryError(f"Upsert failed: {e}")
    
    # =============================================================================
    # Utility Methods
    # =============================================================================
    
    async def table_exists(self, table_name: str) -> bool:
        """Check if table exists."""
        try:
            await self._make_request(
                "GET", 
                table_name, 
                params={"limit": "0"}, 
                use_service_role=True
            )
            return True
        except SupabaseQueryError:
            return False
    
    async def count_rows(self, table: str, filters: Optional[Dict] = None) -> int:
        """Count rows in table."""
        params = {"select": "*", "head": "true"}
        
        if filters:
            for key, value in filters.items():
                params[f"{key}"] = f"eq.{value}"
        
        try:
            result = await self._make_request(
                "HEAD", 
                table, 
                params=params, 
                use_service_role=True
            )
            
            # Extract count from Content-Range header
            # This is a simplified implementation
            return 0  # Would need to parse the actual header
            
        except Exception as e:
            logger.error(f"Count operation failed: {e}")
            return 0
    
    async def execute_query(
        self,
        table: str,
        operation: str,
        data: Optional[Dict] = None,
        filters: Optional[Dict] = None
    ) -> Any:
        """Execute a database query based on operation type."""
        try:
            if operation == "select":
                return await self.select(table, filters=filters)
            elif operation == "insert":
                return await self.insert(table, data)
            elif operation == "update":
                return await self.update(table, data, filters)
            elif operation == "delete":
                return await self.delete(table, filters)
            elif operation == "upsert":
                return await self.upsert(table, data)
            else:
                raise ValueError(f"Unknown operation: {operation}")
                
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise
    
    # =============================================================================
    # Real-time Subscriptions
    # =============================================================================
    
    async def setup_realtime_subscription(
        self,
        table: str,
        columns: Optional[List[str]] = None,
        callback: Optional[Callable] = None
    ) -> str:
        """Setup real-time subscription for table changes."""
        subscription_id = f"{table}_{datetime.now().timestamp()}"
        
        try:
            # This is a simplified implementation
            # In a real implementation, you would establish a WebSocket connection
            subscription_config = {
                "table": table,
                "columns": columns or ["*"],
                "callback": callback,
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            self.realtime_subscriptions[subscription_id] = subscription_config
            
            logger.info(f"Real-time subscription created for table '{table}'")
            return subscription_id
            
        except Exception as e:
            logger.error(f"Failed to setup real-time subscription: {e}")
            raise
    
    async def _unsubscribe_realtime(self, subscription_id: str) -> None:
        """Unsubscribe from real-time updates."""
        if subscription_id in self.realtime_subscriptions:
            del self.realtime_subscriptions[subscription_id]
            logger.info(f"Unsubscribed from real-time subscription: {subscription_id}")
    
    # =============================================================================
    # Storage Operations
    # =============================================================================
    
    async def upload_file(
        self,
        bucket: str,
        file_path: str,
        file_data: bytes,
        content_type: Optional[str] = None
    ) -> Dict:
        """Upload file to Supabase Storage."""
        try:
            headers = self._get_default_headers(use_service_role=True)
            if content_type:
                headers["Content-Type"] = content_type
            
            endpoint = f"object/{bucket}/{file_path}"
            
            # Use storage URL
            url = urljoin(self.storage_url, endpoint)
            
            async with self.session.post(url, data=file_data, headers=headers) as response:
                if response.status >= 400:
                    error_text = await response.text()
                    raise SupabaseError(f"File upload failed: {error_text}")
                
                result = await response.json()
                logger.info(f"File uploaded successfully: {file_path}")
                return result
                
        except Exception as e:
            logger.error(f"File upload failed: {e}")
            raise SupabaseError(f"Upload failed: {e}")
    
    async def download_file(self, bucket: str, file_path: str) -> bytes:
        """Download file from Supabase Storage."""
        try:
            headers = self._get_default_headers(use_service_role=True)
            endpoint = f"object/{bucket}/{file_path}"
            url = urljoin(self.storage_url, endpoint)
            
            async with self.session.get(url, headers=headers) as response:
                if response.status >= 400:
                    error_text = await response.text()
                    raise SupabaseError(f"File download failed: {error_text}")
                
                file_data = await response.read()
                logger.info(f"File downloaded successfully: {file_path}")
                return file_data
                
        except Exception as e:
            logger.error(f"File download failed: {e}")
            raise SupabaseError(f"Download failed: {e}")
    
    async def delete_file(self, bucket: str, file_path: str) -> bool:
        """Delete file from Supabase Storage."""
        try:
            headers = self._get_default_headers(use_service_role=True)
            endpoint = f"object/{bucket}/{file_path}"
            url = urljoin(self.storage_url, endpoint)
            
            async with self.session.delete(url, headers=headers) as response:
                if response.status >= 400:
                    error_text = await response.text()
                    raise SupabaseError(f"File deletion failed: {error_text}")
                
                logger.info(f"File deleted successfully: {file_path}")
                return True
                
        except Exception as e:
            logger.error(f"File deletion failed: {e}")
            return False
    
    # =============================================================================
    # Health and Monitoring
    # =============================================================================
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check."""
        try:
            # Test database connection
            await self._test_connection()
            
            # Check subscription count
            subscription_count = len(self.realtime_subscriptions)
            
            return {
                "status": "healthy",
                "database": "connected",
                "realtime_subscriptions": subscription_count,
                "session_active": self.session is not None and not self.session.closed,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

# =============================================================================
# Factory Functions
# =============================================================================

def create_supabase_client(
    url: Optional[str] = None,
    key: Optional[str] = None,
    service_role_key: Optional[str] = None
) -> SupabaseManager:
    """Create a Supabase client instance."""
    return SupabaseManager(
        url=url or os.getenv('SUPABASE_URL', ''),
        key=key or os.getenv('SUPABASE_ANON_KEY', ''),
        service_role_key=service_role_key or os.getenv('SUPABASE_SERVICE_ROLE_KEY', '')
    )

# Global client instance
_global_client: Optional[SupabaseManager] = None

async def get_supabase_client() -> SupabaseManager:
    """Get or create global Supabase client."""
    global _global_client
    
    if _global_client is None:
        _global_client = create_supabase_client()
        await _global_client.initialize()
    
    return _global_client

# =============================================================================
# Exports
# =============================================================================

__all__ = [
    'SupabaseManager',
    'SupabaseConfig',
    'SupabaseError',
    'SupabaseConnectionError',
    'SupabaseAuthError',
    'SupabaseQueryError',
    'create_supabase_client',
    'get_supabase_client'
]