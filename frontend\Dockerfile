# =============================================================================
# VESLINT Frontend Dockerfile - Next.js Application
# =============================================================================
# Multi-stage Docker build for optimal production deployment
# Supports both development and production environments

# =============================================================================
# Base Stage - Common dependencies
# =============================================================================
FROM node:18-alpine AS base

# Set working directory
WORKDIR /app

# Install system dependencies for alpine
RUN apk add --no-cache \
    libc6-compat \
    dumb-init

# Set npm configuration for better performance
RUN npm config set registry https://registry.npmjs.org/
RUN npm config set fetch-retries 3
RUN npm config set fetch-retry-factor 10
RUN npm config set fetch-retry-mintimeout 10000
RUN npm config set fetch-retry-maxtimeout 60000

# =============================================================================
# Dependencies Stage - Install dependencies
# =============================================================================
FROM base AS deps

# Copy package files
COPY package*.json ./

# Install dependencies with exact versions for reproducibility
RUN npm ci --only=production --no-audit --no-fund

# Install development dependencies in separate layer
COPY package*.json ./
RUN npm ci --no-audit --no-fund

# =============================================================================
# Development Stage - Development environment
# =============================================================================
FROM base AS development

# Copy node_modules from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy source code
COPY . .

# Create non-root user for security
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
RUN chown -R nextjs:nodejs /app
USER nextjs

# Expose development port
EXPOSE 3000

# Set environment variables
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

# Start development server
CMD ["npm", "run", "dev"]

# =============================================================================
# Builder Stage - Build the application
# =============================================================================
FROM base AS builder

# Copy node_modules from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy source code
COPY . .

# Set build environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Build arguments for build-time environment variables
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_SUPABASE_URL
ARG NEXT_PUBLIC_SUPABASE_ANON_KEY
ARG NEXT_PUBLIC_FIREBASE_API_KEY
ARG NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
ARG NEXT_PUBLIC_FIREBASE_PROJECT_ID
ARG NEXT_PUBLIC_APP_NAME
ARG NEXT_PUBLIC_APP_VERSION
ARG NEXT_PUBLIC_ENVIRONMENT

# Set environment variables for build
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_SUPABASE_URL=$NEXT_PUBLIC_SUPABASE_URL
ENV NEXT_PUBLIC_SUPABASE_ANON_KEY=$NEXT_PUBLIC_SUPABASE_ANON_KEY
ENV NEXT_PUBLIC_FIREBASE_API_KEY=$NEXT_PUBLIC_FIREBASE_API_KEY
ENV NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=$NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
ENV NEXT_PUBLIC_FIREBASE_PROJECT_ID=$NEXT_PUBLIC_FIREBASE_PROJECT_ID
ENV NEXT_PUBLIC_APP_NAME=$NEXT_PUBLIC_APP_NAME
ENV NEXT_PUBLIC_APP_VERSION=$NEXT_PUBLIC_APP_VERSION
ENV NEXT_PUBLIC_ENVIRONMENT=$NEXT_PUBLIC_ENVIRONMENT

# TypeScript type checking
RUN npm run type-check

# ESLint checking
RUN npm run lint

# Build the application
RUN npm run build

# =============================================================================
# Production Stage - Final production image
# =============================================================================
FROM node:18-alpine AS production

# Install system dependencies
RUN apk add --no-cache \
    dumb-init \
    curl

# Set working directory
WORKDIR /app

# Create non-root user for security
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application from builder stage
COPY --from=builder /app/public ./public

# Copy Next.js build output
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Copy package.json for metadata
COPY --chown=nextjs:nodejs package.json ./

# Set ownership for nextjs user
RUN chown -R nextjs:nodejs /app

# Switch to non-root user
USER nextjs

# Set production environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Expose port
EXPOSE 3000

# Health check for production
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "server.js"]

# =============================================================================
# Vercel Stage - Optimized for Vercel deployment
# =============================================================================
FROM base AS vercel

# Copy node_modules from deps stage (production only)
COPY --from=deps /app/node_modules ./node_modules

# Copy source code
COPY . .

# Set environment for Vercel
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Build for Vercel
RUN npm run build

# Export static files for Vercel
RUN npm run export

# =============================================================================
# Testing Stage - For running tests
# =============================================================================
FROM base AS testing

# Copy node_modules with dev dependencies
COPY --from=deps /app/node_modules ./node_modules

# Copy source code
COPY . .

# Install browsers for E2E testing
RUN npx playwright install chromium firefox webkit
RUN npx playwright install-deps

# Set test environment
ENV NODE_ENV=test
ENV CI=true

# Run tests
RUN npm run test -- --coverage --watchAll=false
RUN npm run lint
RUN npm run type-check

# =============================================================================
# Build Arguments and Labels
# =============================================================================

# Build arguments for metadata
ARG BUILD_DATE
ARG VERSION
ARG VCS_REF

# Add labels for metadata
LABEL org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="VESLINT Frontend" \
      org.label-schema.description="Next.js frontend for VESLINT maritime vessel classification" \
      org.label-schema.version=$VERSION \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.vcs-url="https://github.com/veslint/veslint" \
      org.label-schema.vendor="VESLINT Team" \
      org.label-schema.schema-version="1.0" \
      maintainer="VESLINT Team <<EMAIL>>"

# =============================================================================
# Build Examples
# =============================================================================

# Development build:
# docker build --target development -t veslint-frontend:dev .
# docker run -p 3000:3000 -v $(pwd):/app veslint-frontend:dev

# Production build:
# docker build --target production \
#   --build-arg NEXT_PUBLIC_API_URL=https://api.veslint.com \
#   --build-arg NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co \
#   --build-arg NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key \
#   -t veslint-frontend:prod .

# Testing build:
# docker build --target testing -t veslint-frontend:test .

# Vercel build:
# docker build --target vercel -t veslint-frontend:vercel .

# =============================================================================
# Docker Compose Integration
# =============================================================================

# This Dockerfile is designed to work with docker-compose.yml
# See docker-compose.yml for service configuration

# =============================================================================
# Security Notes
# =============================================================================

# 1. Uses non-root user (nextjs:nodejs)
# 2. Minimal base image (alpine)
# 3. No sensitive data in layers
# 4. Health checks included
# 5. Proper signal handling with dumb-init

# =============================================================================
# Performance Optimizations
# =============================================================================

# 1. Multi-stage build reduces final image size
# 2. Separate dependency installation for better caching
# 3. Standalone build for faster startup
# 4. Optimized layer ordering
# 5. Production-only dependencies in final image

# =============================================================================
# Maintenance
# =============================================================================

# Regular maintenance tasks:
# 1. Update base Node.js version
# 2. Update system dependencies
# 3. Review and update build arguments
# 4. Test builds with new Next.js versions
# 5. Security scanning with docker scan