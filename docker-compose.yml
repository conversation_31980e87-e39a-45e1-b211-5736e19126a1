# =============================================================================
# VESLINT Docker Compose Configuration
# =============================================================================
# Complete local development environment with all services
# Includes backend API, frontend, database, and supporting services

version: '3.8'

services:
  # =============================================================================
  # Supabase Local Development Stack
  # =============================================================================
  
  supabase-db:
    image: supabase/postgres:**********
    container_name: veslint-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
      - ./database/migrations:/docker-entrypoint-initdb.d
    networks:
      - veslint-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
  
  supabase-auth:
    image: supabase/gotrue:v2.99.0
    container_name: veslint-auth
    restart: unless-stopped
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      API_EXTERNAL_URL: http://localhost:8000
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: *********************************************/postgres?search_path=auth
      GOTRUE_SITE_URL: http://localhost:3000
      GOTRUE_URI_ALLOW_LIST: http://localhost:3000
      GOTRUE_DISABLE_SIGNUP: false
      GOTRUE_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      GOTRUE_JWT_EXP: 3600
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
      GOTRUE_JWT_ADMIN_GROUP_NAME: service_role
      GOTRUE_PASSWORD_MIN_LENGTH: 6
      GOTRUE_SMS_AUTOCONFIRM: true
      GOTRUE_MAILER_AUTOCONFIRM: true
    ports:
      - "9999:9999"
    depends_on:
      supabase-db:
        condition: service_healthy
    networks:
      - veslint-network
  
  supabase-rest:
    image: postgrest/postgrest:v12.0.1
    container_name: veslint-rest
    restart: unless-stopped
    environment:
      PGRST_DB_URI: *********************************************/postgres
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      PGRST_DB_USE_LEGACY_GUCS: false
    ports:
      - "3001:3000"
    depends_on:
      supabase-db:
        condition: service_healthy
    networks:
      - veslint-network
  
  supabase-realtime:
    image: supabase/realtime:v2.25.50
    container_name: veslint-realtime
    restart: unless-stopped
    environment:
      PORT: 4000
      DB_HOST: supabase-db
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_NAME: postgres
      DB_AFTER_CONNECT_QUERY: 'SET search_path TO _realtime'
      DB_ENC_KEY: supabaserealtime
      API_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      FLY_ALLOC_ID: fly123
      FLY_APP_NAME: realtime
      SECRET_KEY_BASE: UpNVntn3cDxHJpq99YMc1T1AQgQpc8kfYTuRgBiYa15BLrx8etQoXz3gZv1/u2oq
      ERL_AFLAGS: -proto_dist inet_tcp
      ENABLE_TAILSCALE: false
      DNS_NODES: "''"
    ports:
      - "4000:4000"
    depends_on:
      supabase-db:
        condition: service_healthy
    networks:
      - veslint-network
  
  supabase-storage:
    image: supabase/storage-api:v0.40.4
    container_name: veslint-storage
    restart: unless-stopped
    environment:
      ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
      POSTGREST_URL: http://supabase-rest:3000
      PGRST_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      DATABASE_URL: *********************************************/postgres
      FILE_SIZE_LIMIT: 52428800
      STORAGE_BACKEND: file
      FILE_STORAGE_BACKEND_PATH: /var/lib/storage
      TENANT_ID: stub
      REGION: stub
      GLOBAL_S3_BUCKET: stub
      ENABLE_IMAGE_TRANSFORMATION: true
      IMGPROXY_URL: http://supabase-imgproxy:5001
    ports:
      - "5000:5000"
    volumes:
      - storage_data:/var/lib/storage
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-rest:
        condition: service_started
    networks:
      - veslint-network
  
  supabase-imgproxy:
    image: darthsim/imgproxy:v3.8.0
    container_name: veslint-imgproxy
    restart: unless-stopped
    environment:
      IMGPROXY_BIND: 0.0.0.0:5001
      IMGPROXY_LOCAL_FILESYSTEM_ROOT: /
      IMGPROXY_USE_ETAG: true
      IMGPROXY_ENABLE_WEBP_DETECTION: true
    volumes:
      - storage_data:/var/lib/storage:ro
    networks:
      - veslint-network
  
  # =============================================================================
  # VESLINT Backend API
  # =============================================================================
  
  veslint-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: veslint-backend
    restart: unless-stopped
    environment:
      # Application Configuration
      ENVIRONMENT: development
      LOG_LEVEL: DEBUG
      API_VERSION: v1
      
      # Database Configuration
      SUPABASE_URL: http://supabase-rest:3000
      SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SUPABASE_SERVICE_ROLE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
      
      # CORS Configuration
      CORS_ORIGINS: http://localhost:3000,http://localhost:3001
      
      # Performance Configuration
      MAX_WORKERS: 2
      WORKER_TIMEOUT: 120
      KEEP_ALIVE: 2
      
      # ML Model Configuration
      MODEL_CACHE_SIZE: 50
      FEATURE_CACHE_TTL: 300
      PREDICTION_TIMEOUT: 30
      
      # Storage Configuration
      STORAGE_URL: http://supabase-storage:5000
      
      # Optional: Firebase Auth (if enabled)
      # FIREBASE_PROJECT_ID: your-project-id
      
      # Optional: HuggingFace (if using cloud model)
      # HUGGINGFACE_API_TOKEN: your-token
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/venv
      - ./ml-model/assets:/app/assets
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-rest:
        condition: service_started
      supabase-auth:
        condition: service_started
    networks:
      - veslint-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
  
  # =============================================================================
  # VESLINT Frontend
  # =============================================================================
  
  veslint-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: veslint-frontend
    restart: unless-stopped
    environment:
      # Next.js Configuration
      NODE_ENV: development
      PORT: 3000
      
      # API Configuration
      NEXT_PUBLIC_API_URL: http://localhost:8000
      NEXT_PUBLIC_API_VERSION: v1
      
      # Database Configuration
      NEXT_PUBLIC_SUPABASE_URL: http://localhost:3001
      NEXT_PUBLIC_SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      
      # Application Configuration
      NEXT_PUBLIC_APP_NAME: VESLINT
      NEXT_PUBLIC_APP_VERSION: 2.0.0
      NEXT_PUBLIC_ENVIRONMENT: development
      
      # Feature Flags
      NEXT_PUBLIC_ENABLE_ANALYTICS: false
      NEXT_PUBLIC_ENABLE_ERROR_REPORTING: false
      NEXT_PUBLIC_ENABLE_REAL_TIME: true
      
      # Development Configuration
      WATCHPACK_POLLING: true
      CHOKIDAR_USEPOLLING: true
      
      # Optional: Firebase Auth (if enabled)
      # NEXT_PUBLIC_FIREBASE_API_KEY: your-api-key
      # NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: your-project.firebaseapp.com
      # NEXT_PUBLIC_FIREBASE_PROJECT_ID: your-project-id
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      veslint-backend:
        condition: service_healthy
    networks:
      - veslint-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
  
  # =============================================================================
  # Development Tools
  # =============================================================================
  
  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: veslint-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - veslint-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
  
  # Minio for S3-compatible storage (optional)
  minio:
    image: minio/minio:latest
    container_name: veslint-minio
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - veslint-network
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
  
  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: veslint-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      supabase-db:
        condition: service_healthy
    networks:
      - veslint-network
    profiles:
      - tools

# =============================================================================
# Networks
# =============================================================================

networks:
  veslint-network:
    driver: bridge
    name: veslint-network

# =============================================================================
# Volumes
# =============================================================================

volumes:
  db_data:
    name: veslint-db-data
  storage_data:
    name: veslint-storage-data
  redis_data:
    name: veslint-redis-data
  minio_data:
    name: veslint-minio-data
  pgadmin_data:
    name: veslint-pgadmin-data

# =============================================================================
# Development Profiles
# =============================================================================
# 
# Usage Examples:
# 
# Start core services:
# docker-compose up -d
# 
# Start with development tools:
# docker-compose --profile tools up -d
# 
# Start only database services:
# docker-compose up -d supabase-db supabase-rest supabase-auth
# 
# Start only the backend:
# docker-compose up -d veslint-backend
# 
# Start only the frontend:
# docker-compose up -d veslint-frontend
# 
# View logs:
# docker-compose logs -f veslint-backend
# docker-compose logs -f veslint-frontend
# 
# Stop all services:
# docker-compose down
# 
# Stop and remove volumes:
# docker-compose down -v
# 
# Rebuild services:
# docker-compose build --no-cache
# docker-compose up -d --force-recreate
#
# =============================================================================