"""
VESLINT Backend API
FastAPI application for maritime vessel classification
Runs on Render with Hugging Face model integration
"""

import os
import logging
from contextlib import asynccontextmanager
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from api.routes import jobs, vessels
from api.middleware import setup_middleware
from utils.logging import setup_logging
from database.supabase_client import test_connection

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting VESLINT API...")
    
    # Test database connection
    try:
        await test_connection()
        logger.info("✅ Supabase connection successful")
    except Exception as e:
        logger.error(f"❌ Supabase connection failed: {e}")
    
    # Test Hugging Face connectivity (optional)
    try:
        from services.ml_service import test_model_connection
        await test_model_connection()
        logger.info("✅ Hugging Face model connection successful")
    except Exception as e:
        logger.warning(f"⚠️ Hugging Face model connection warning: {e}")
    
    logger.info("🚀 VESLINT API started successfully")
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down VESLINT API...")

# Create FastAPI application
app = FastAPI(
    title="VESLINT API",
    description="Maritime Vessel Classification API",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Setup middleware
setup_middleware(app)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint for Render"""
    return {
        "status": "healthy",
        "service": "VESLINT API",
        "version": "2.0.0",
        "environment": os.getenv("ENVIRONMENT", "production")
    }

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "VESLINT Maritime Vessel Classification API",
        "version": "2.0.0",
        "docs": "/docs",
        "health": "/health"
    }

# Include routers
app.include_router(jobs.router, prefix="/api/v1/jobs", tags=["jobs"])
app.include_router(vessels.router, prefix="/api/v1/vessels", tags=["vessels"])

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Global exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "request_id": getattr(request.state, "request_id", None)
        }
    )

if __name__ == "__main__":
    # For local development
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        log_level="info"
    )