# VESLINT Backend Dependencies
# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# HTTP client for Hugging Face API
httpx==0.24.1
requests==2.31.0

# Data processing and ML
pandas==2.1.4
numpy==1.24.4
scikit-learn==1.3.2
joblib==1.3.2

# Database and storage
supabase==2.0.3
postgrest==0.13.0

# Authentication
firebase-admin==6.4.0
python-jose[cryptography]==3.3.0

# File handling
python-multipart==0.0.6
aiofiles==23.2.1

# Data validation
pydantic==2.5.0
pydantic-settings==2.1.0

# Utilities
python-dotenv==1.0.0
structlog==23.2.0

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1

# Production
gunicorn==21.2.0