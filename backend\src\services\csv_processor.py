"""
CSV Processing Service
Handles AIS data validation, parsing, and preprocessing
"""

import logging
import pandas as pd
import numpy as np
import io
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime
import chardet

from utils.constants import REQUIRED_CSV_COLUMNS, AIS_VALIDATION, PROCESSING_CONFIG

logger = logging.getLogger(__name__)

async def process_uploaded_csv(file_content: bytes, filename: str) -> Dict[str, Any]:
    """
    Process uploaded CSV file and extract vessel data
    
    Args:
        file_content: Raw CSV file content
        filename: Original filename
        
    Returns:
        Processing result with vessel data and metadata
    """
    try:
        logger.info(f"Processing CSV file: {filename}")
        
        # Detect encoding
        encoding = detect_encoding(file_content)
        logger.info(f"Detected encoding: {encoding}")
        
        # Decode content
        try:
            csv_text = file_content.decode(encoding)
        except UnicodeDecodeError:
            # Fallback to UTF-8 with error handling
            csv_text = file_content.decode('utf-8', errors='replace')
            logger.warning("Used UTF-8 with error replacement for problematic characters")
        
        # Parse CSV
        df = pd.read_csv(io.StringIO(csv_text))
        
        # Validate and clean data
        validation_result = await validate_ais_data(df)
        if not validation_result['valid']:
            raise ValueError(f"Invalid AIS data: {', '.join(validation_result['errors'])}")
        
        # Clean and preprocess data
        cleaned_df = await clean_ais_data(df)
        
        # Extract vessel information
        vessel_info = await extract_vessel_info(cleaned_df)
        
        result = {
            'success': True,
            'filename': filename,
            'total_records': len(df),
            'valid_records': len(cleaned_df),
            'vessel_count': len(vessel_info['vessels']),
            'vessels': vessel_info['vessels'],
            'data_quality': vessel_info['data_quality'],
            'processing_metadata': {
                'encoding': encoding,
                'file_size_bytes': len(file_content),
                'columns': list(df.columns),
                'processing_timestamp': datetime.utcnow().isoformat()
            }
        }
        
        logger.info(f"Successfully processed {filename}: {result['vessel_count']} vessels from {result['total_records']} records")
        return result
        
    except Exception as e:
        logger.error(f"Failed to process CSV {filename}: {e}")
        return {
            'success': False,
            'filename': filename,
            'error': str(e),
            'processing_metadata': {
                'file_size_bytes': len(file_content),
                'processing_timestamp': datetime.utcnow().isoformat()
            }
        }

def detect_encoding(file_content: bytes) -> str:
    """Detect file encoding"""
    try:
        result = chardet.detect(file_content[:10000])  # Check first 10KB
        encoding = result.get('encoding', 'utf-8')
        confidence = result.get('confidence', 0)
        
        logger.info(f"Encoding detection: {encoding} (confidence: {confidence:.2f})")
        
        # Use UTF-8 if confidence is low
        if confidence < 0.7:
            logger.warning(f"Low confidence encoding detection, using UTF-8")
            return 'utf-8'
        
        return encoding
        
    except Exception as e:
        logger.warning(f"Encoding detection failed: {e}, using UTF-8")
        return 'utf-8'

async def validate_ais_data(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Validate AIS data structure and content
    
    Args:
        df: DataFrame with AIS data
        
    Returns:
        Validation result with errors and warnings
    """
    errors = []
    warnings = []
    
    # Check required columns
    missing_columns = [col for col in REQUIRED_CSV_COLUMNS if col not in df.columns]
    if missing_columns:
        errors.append(f"Missing required columns: {missing_columns}")
        return {'valid': False, 'errors': errors, 'warnings': warnings}
    
    # Check data types and ranges
    try:
        # Validate latitude
        if 'lat' in df.columns:
            lat_invalid = df[(df['lat'] < AIS_VALIDATION['lat']['min']) | 
                           (df['lat'] > AIS_VALIDATION['lat']['max'])]
            if len(lat_invalid) > 0:
                warnings.append(f"{len(lat_invalid)} records with invalid latitude values")
        
        # Validate longitude
        if 'lon' in df.columns:
            lon_invalid = df[(df['lon'] < AIS_VALIDATION['lon']['min']) | 
                           (df['lon'] > AIS_VALIDATION['lon']['max'])]
            if len(lon_invalid) > 0:
                warnings.append(f"{len(lon_invalid)} records with invalid longitude values")
        
        # Validate speed over ground
        if 'sog' in df.columns:
            sog_invalid = df[(df['sog'] < AIS_VALIDATION['sog']['min']) | 
                           (df['sog'] > AIS_VALIDATION['sog']['max'])]
            if len(sog_invalid) > 0:
                warnings.append(f"{len(sog_invalid)} records with unrealistic speed values")
        
        # Validate course over ground
        if 'cog' in df.columns:
            cog_invalid = df[(df['cog'] < AIS_VALIDATION['cog']['min']) | 
                           (df['cog'] > AIS_VALIDATION['cog']['max'])]
            if len(cog_invalid) > 0:
                warnings.append(f"{len(cog_invalid)} records with invalid course values")
        
        # Validate heading
        if 'heading' in df.columns:
            heading_invalid = df[(df['heading'] < AIS_VALIDATION['heading']['min']) | 
                               (df['heading'] > AIS_VALIDATION['heading']['max'])]
            if len(heading_invalid) > 0:
                warnings.append(f"{len(heading_invalid)} records with invalid heading values")
        
        # Validate MMSI
        if 'mmsi' in df.columns:
            df['mmsi'] = df['mmsi'].astype(str)
            mmsi_invalid = df[
                (df['mmsi'].str.len() < AIS_VALIDATION['mmsi']['min_length']) |
                (df['mmsi'].str.len() > AIS_VALIDATION['mmsi']['max_length']) |
                (~df['mmsi'].str.isdigit())
            ]
            if len(mmsi_invalid) > 0:
                warnings.append(f"{len(mmsi_invalid)} records with invalid MMSI format")
        
        # Check for missing timestamps
        if 'timestamp' in df.columns:
            timestamp_missing = df[df['timestamp'].isna() | (df['timestamp'] == '')]
            if len(timestamp_missing) > 0:
                warnings.append(f"{len(timestamp_missing)} records with missing timestamps")
        
        # Check vessel count
        vessel_count = df['mmsi'].nunique() if 'mmsi' in df.columns else 0
        if vessel_count == 0:
            errors.append("No valid vessels found in data")
        elif vessel_count > PROCESSING_CONFIG['max_concurrent_jobs'] * 1000:
            warnings.append(f"Large dataset with {vessel_count} vessels may take significant time to process")
        
        # Check minimum points per vessel
        if vessel_count > 0:
            vessel_point_counts = df.groupby('mmsi').size()
            insufficient_vessels = vessel_point_counts[vessel_point_counts < PROCESSING_CONFIG['min_points_per_vessel']]
            if len(insufficient_vessels) > 0:
                warnings.append(f"{len(insufficient_vessels)} vessels have fewer than {PROCESSING_CONFIG['min_points_per_vessel']} AIS points")
        
    except Exception as e:
        errors.append(f"Data validation error: {e}")
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'warnings': warnings,
        'vessel_count': vessel_count if 'vessel_count' in locals() else 0
    }

async def clean_ais_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Clean and preprocess AIS data
    
    Args:
        df: Raw AIS DataFrame
        
    Returns:
        Cleaned DataFrame
    """
    logger.info(f"Cleaning AIS data: {len(df)} records")
    
    # Create a copy to avoid modifying original
    cleaned_df = df.copy()
    
    # Convert MMSI to string and clean
    cleaned_df['mmsi'] = cleaned_df['mmsi'].astype(str).str.strip()
    
    # Remove invalid MMSI values
    cleaned_df = cleaned_df[
        (cleaned_df['mmsi'].str.len() >= AIS_VALIDATION['mmsi']['min_length']) &
        (cleaned_df['mmsi'].str.len() <= AIS_VALIDATION['mmsi']['max_length']) &
        (cleaned_df['mmsi'].str.isdigit())
    ]
    
    # Parse timestamps
    try:
        cleaned_df['timestamp'] = pd.to_datetime(cleaned_df['timestamp'], errors='coerce')
        # Remove records with invalid timestamps
        cleaned_df = cleaned_df.dropna(subset=['timestamp'])
    except Exception as e:
        logger.warning(f"Timestamp parsing issues: {e}")
    
    # Clean numeric columns
    numeric_columns = ['lat', 'lon', 'sog', 'cog', 'heading']
    for col in numeric_columns:
        if col in cleaned_df.columns:
            # Convert to numeric, invalid values become NaN
            cleaned_df[col] = pd.to_numeric(cleaned_df[col], errors='coerce')
    
    # Apply range filters
    if 'lat' in cleaned_df.columns:
        cleaned_df = cleaned_df[
            (cleaned_df['lat'] >= AIS_VALIDATION['lat']['min']) &
            (cleaned_df['lat'] <= AIS_VALIDATION['lat']['max'])
        ]
    
    if 'lon' in cleaned_df.columns:
        cleaned_df = cleaned_df[
            (cleaned_df['lon'] >= AIS_VALIDATION['lon']['min']) &
            (cleaned_df['lon'] <= AIS_VALIDATION['lon']['max'])
        ]
    
    if 'sog' in cleaned_df.columns:
        cleaned_df = cleaned_df[
            (cleaned_df['sog'] >= AIS_VALIDATION['sog']['min']) &
            (cleaned_df['sog'] <= AIS_VALIDATION['sog']['max'])
        ]
    
    if 'cog' in cleaned_df.columns:
        cleaned_df = cleaned_df[
            (cleaned_df['cog'] >= AIS_VALIDATION['cog']['min']) &
            (cleaned_df['cog'] <= AIS_VALIDATION['cog']['max'])
        ]
    
    if 'heading' in cleaned_df.columns:
        cleaned_df = cleaned_df[
            (cleaned_df['heading'] >= AIS_VALIDATION['heading']['min']) &
            (cleaned_df['heading'] <= AIS_VALIDATION['heading']['max'])
        ]
    
    # Remove duplicate records (same vessel, same timestamp)
    if 'mmsi' in cleaned_df.columns and 'timestamp' in cleaned_df.columns:
        initial_count = len(cleaned_df)
        cleaned_df = cleaned_df.drop_duplicates(subset=['mmsi', 'timestamp'])
        duplicates_removed = initial_count - len(cleaned_df)
        if duplicates_removed > 0:
            logger.info(f"Removed {duplicates_removed} duplicate records")
    
    # Sort by vessel and timestamp
    cleaned_df = cleaned_df.sort_values(['mmsi', 'timestamp'])
    
    # Remove vessels with insufficient data points
    vessel_counts = cleaned_df['mmsi'].value_counts()
    valid_vessels = vessel_counts[vessel_counts >= PROCESSING_CONFIG['min_points_per_vessel']].index
    cleaned_df = cleaned_df[cleaned_df['mmsi'].isin(valid_vessels)]
    
    logger.info(f"Cleaned data: {len(cleaned_df)} valid records for {cleaned_df['mmsi'].nunique()} vessels")
    
    return cleaned_df

async def extract_vessel_info(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Extract vessel information and basic statistics
    
    Args:
        df: Cleaned AIS DataFrame
        
    Returns:
        Vessel information and data quality metrics
    """
    vessels = []
    
    for mmsi, vessel_data in df.groupby('mmsi'):
        vessel_info = {
            'mmsi': str(mmsi),
            'ais_points_count': len(vessel_data),
            'time_span_hours': 0,
            'geographic_bounds': {},
            'speed_stats': {},
            'data_quality_score': 0
        }
        
        # Calculate time span
        if len(vessel_data) > 1:
            time_span = vessel_data['timestamp'].max() - vessel_data['timestamp'].min()
            vessel_info['time_span_hours'] = time_span.total_seconds() / 3600.0
        
        # Geographic bounds
        if 'lat' in vessel_data.columns and 'lon' in vessel_data.columns:
            vessel_info['geographic_bounds'] = {
                'min_lat': float(vessel_data['lat'].min()),
                'max_lat': float(vessel_data['lat'].max()),
                'min_lon': float(vessel_data['lon'].min()),
                'max_lon': float(vessel_data['lon'].max())
            }
        
        # Speed statistics
        if 'sog' in vessel_data.columns:
            vessel_info['speed_stats'] = {
                'avg_speed_knots': float(vessel_data['sog'].mean()),
                'max_speed_knots': float(vessel_data['sog'].max()),
                'min_speed_knots': float(vessel_data['sog'].min())
            }
        
        # Calculate data quality score
        quality_score = calculate_data_quality_score(vessel_data)
        vessel_info['data_quality_score'] = quality_score
        
        vessels.append(vessel_info)
    
    # Overall data quality metrics
    data_quality = {
        'total_vessels': len(vessels),
        'high_quality_vessels': len([v for v in vessels if v['data_quality_score'] > 0.8]),
        'medium_quality_vessels': len([v for v in vessels if 0.5 <= v['data_quality_score'] <= 0.8]),
        'low_quality_vessels': len([v for v in vessels if v['data_quality_score'] < 0.5]),
        'average_points_per_vessel': np.mean([v['ais_points_count'] for v in vessels]),
        'average_time_span_hours': np.mean([v['time_span_hours'] for v in vessels if v['time_span_hours'] > 0])
    }
    
    return {
        'vessels': vessels,
        'data_quality': data_quality
    }

def calculate_data_quality_score(vessel_data: pd.DataFrame) -> float:
    """Calculate data quality score for a vessel"""
    score = 0.0
    
    # Points count (more points = better quality)
    points_score = min(1.0, len(vessel_data) / 50.0)  # Normalize to 50 points
    score += points_score * 0.3
    
    # Time span (longer tracking = better)
    if len(vessel_data) > 1:
        time_span_hours = (vessel_data['timestamp'].max() - vessel_data['timestamp'].min()).total_seconds() / 3600.0
        time_score = min(1.0, time_span_hours / 24.0)  # Normalize to 24 hours
        score += time_score * 0.2
    
    # Data completeness (no missing values)
    completeness = 1.0 - (vessel_data.isnull().sum().sum() / (len(vessel_data) * len(vessel_data.columns)))
    score += completeness * 0.2
    
    # Speed consistency (realistic values)
    if 'sog' in vessel_data.columns:
        speed_outliers = len(vessel_data[vessel_data['sog'] > 40])  # > 40 knots unusual
        speed_score = 1.0 - (speed_outliers / len(vessel_data))
        score += speed_score * 0.15
    
    # Regular timing (consistent intervals)
    if len(vessel_data) > 2:
        time_diffs = vessel_data['timestamp'].diff().dt.total_seconds().dropna()
        if len(time_diffs) > 0:
            cv = time_diffs.std() / time_diffs.mean() if time_diffs.mean() > 0 else 1
            timing_score = max(0, 1.0 - cv)  # Lower coefficient of variation = better
            score += timing_score * 0.15
    
    return min(1.0, score)

async def chunk_vessels_for_processing(vessels: List[Dict[str, Any]], chunk_size: int = None) -> List[List[str]]:
    """
    Split vessels into processing chunks
    
    Args:
        vessels: List of vessel information
        chunk_size: Size of each chunk (default from config)
        
    Returns:
        List of vessel MMSI chunks
    """
    if chunk_size is None:
        chunk_size = PROCESSING_CONFIG['chunk_size']
    
    vessel_mmsis = [vessel['mmsi'] for vessel in vessels]
    chunks = []
    
    for i in range(0, len(vessel_mmsis), chunk_size):
        chunk = vessel_mmsis[i:i + chunk_size]
        chunks.append(chunk)
    
    logger.info(f"Split {len(vessel_mmsis)} vessels into {len(chunks)} chunks of size {chunk_size}")
    return chunks